'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { SONNET4_MODEL_ID, GEMINI25_PRO_MODEL_ID } from './_use-model-selection';
import { toast } from 'sonner';

interface ModelToggleProps {
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  canAccessModel: (modelId: string) => boolean;
}

export const ModelToggle: React.FC<ModelToggleProps> = ({
  selectedModel,
  onModelChange,
  canAccessModel,
}) => {
  // Determine if we're in AL1 mode (Claude Sonnet) or normal mode (Gemini 2.5 Pro)
  const isAL1Mode = selectedModel === SONNET4_MODEL_ID;

  const handleToggle = React.useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const newModel = isAL1Mode ? GEMINI25_PRO_MODEL_ID : SONNET4_MODEL_ID;
    const newModeIsAL1 = newModel === SONNET4_MODEL_ID;

    try {
      if (canAccessModel && canAccessModel(newModel)) {
        onModelChange(newModel);

        // Show toast notification
        if (newModeIsAL1) {
          toast.success('Advanced language mode enabled', {
            description: 'Using Claude Sonnet 4 for enhanced capabilities',
            duration: 2000,
          });
        } else {
          toast.success('Quick mode enabled', {
            description: 'Using Gemini 2.5 Pro for faster responses',
            duration: 2000,
          });
        }
      } else {
        toast.error('Model not accessible', {
          description: 'Please check your subscription status',
          duration: 3000,
        });
      }
    } catch (error) {
      console.warn('Error checking model access:', error);
      // Fallback: just try to change the model
      onModelChange(newModel);

      // Show fallback toast
      if (newModeIsAL1) {
        toast.success('Advanced language mode enabled');
      } else {
        toast.success('Quick mode enabled');
      }
    }
  }, [isAL1Mode, onModelChange, canAccessModel]);

  return (
    <div className="flex items-center gap-2">
      <button
        onClick={handleToggle}
        className={cn(
          "relative inline-flex h-5 w-9 items-center rounded-full transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 cursor-pointer hover:opacity-80",
          isAL1Mode
            ? "bg-blue-600 hover:bg-blue-700"
            : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"
        )}
        type="button"
        role="switch"
        aria-checked={isAL1Mode}
        aria-label={`Switch to ${isAL1Mode ? 'Quick mode' : 'Advanced language mode'}`}
        tabIndex={0}
      >
        {/* Toggle circle */}
        <span
          className={cn(
            "inline-block h-3 w-3 transform rounded-full bg-white transition-transform duration-200 ease-in-out shadow-sm",
            isAL1Mode ? "translate-x-5" : "translate-x-1"
          )}
        />
      </button>

      {/* Mode label */}
      <span
        className={cn(
          "text-xs text-muted-foreground cursor-pointer select-none",
          isAL1Mode ? "font-bold text-blue-600 dark:text-blue-400" : "font-normal"
        )}
        onClick={handleToggle}
      >
        {isAL1Mode ? "AL1" : "AL0"}
      </span>
    </div>
  );
};
